<?php

namespace App\Service;

use App\Entity\GVend;
use App\Repository\GVendRepository;
use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

/**
 * Handles errors and retry logic for GVend processing
 */
class GVendErrorHandler
{
    public function __construct(
        private GVendRepository $gVendRepository,
        private Connection $connection,
        private LoggerInterface $logger,
        private ParameterBagInterface $params
    ) {}

    /**
     * Log processing error and update record status
     * 
     * @param GVend $gVend
     * @param \Exception $exception
     * @param int $attemptNumber
     * @return void
     */
    public function handleProcessingError(GVend $gVend, \Exception $exception, int $attemptNumber = 1): void
    {
        $maxRetries = $this->params->get('gvend.error.max_retries');
        
        $errorData = [
            'gvend_id' => $gVend->getId(),
            'imei' => $gVend->getImei(),
            'attempt' => $attemptNumber,
            'max_retries' => $maxRetries,
            'error_message' => $exception->getMessage(),
            'error_class' => get_class($exception),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // Log the error
        $this->logger->error('GVend processing error', [
            'context' => __CLASS__,
            'error_data' => $errorData,
            'exception' => $exception
        ]);

        // Store error details in database
        $this->storeErrorDetails($gVend, $errorData);

        // Determine next status based on attempt number
        if ($attemptNumber >= $maxRetries) {
            // Mark as failed after max retries
            $this->gVendRepository->markAsProcessed($gVend, $this->params->get('gvend.status.failed'));
            $this->logger->warning(sprintf(
                'GVend record %d marked as failed after %d attempts',
                $gVend->getId(),
                $attemptNumber
            ));
        } else {
            // Mark for retry
            $this->gVendRepository->markAsProcessed($gVend, $this->params->get('gvend.status.retry'));
            $this->logger->info(sprintf(
                'GVend record %d marked for retry (attempt %d/%d)',
                $gVend->getId(),
                $attemptNumber,
                $maxRetries
            ));
        }
    }

    /**
     * Store error details in database for debugging
     * 
     * @param GVend $gVend
     * @param array $errorData
     * @return void
     */
    private function storeErrorDetails(GVend $gVend, array $errorData): void
    {
        try {
            $this->connection->insert('gvend_processing_errors', [
                'gvend_id' => $gVend->getId(),
                'imei' => $gVend->getImei(),
                'error_data' => json_encode($errorData),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Don't let error logging break the main process
            $this->logger->error('Failed to store GVend error details', [
                'original_error' => $errorData,
                'storage_error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get records ready for retry
     * 
     * @return array
     */
    public function getRecordsForRetry(): array
    {
        $retryDelay = $this->params->get('gvend.error.retry_delay');
        $retryStatus = $this->params->get('gvend.status.retry');
        
        return $this->gVendRepository->createQueryBuilder('g')
            ->where('g.proc = :retry_status')
            ->andWhere('g.created < :retry_time')
            ->setParameter('retry_status', $retryStatus)
            ->setParameter('retry_time', new \DateTime(sprintf('-%d seconds', $retryDelay)))
            ->orderBy('g.created', 'ASC')
            ->setMaxResults(50)
            ->getQuery()
            ->getResult();
    }

    /**
     * Reset failed records for manual reprocessing
     * 
     * @param array $gVendIds
     * @return int
     */
    public function resetFailedRecords(array $gVendIds): int
    {
        $unprocessedStatus = $this->params->get('gvend.status.unprocessed');
        return $this->gVendRepository->batchUpdateProcessingStatus($gVendIds, $unprocessedStatus);
    }

    /**
     * Get processing statistics
     * 
     * @return array
     */
    public function getProcessingStats(): array
    {
        $stats = [];
        
        $statusCodes = [
            'unprocessed' => $this->params->get('gvend.status.unprocessed'),
            'processed' => $this->params->get('gvend.status.processed'),
            'failed' => $this->params->get('gvend.status.failed'),
            'retry' => $this->params->get('gvend.status.retry')
        ];

        foreach ($statusCodes as $status => $code) {
            $count = $this->gVendRepository->createQueryBuilder('g')
                ->select('COUNT(g.id)')
                ->where('g.proc = :status')
                ->setParameter('status', $code)
                ->getQuery()
                ->getSingleScalarResult();
            
            $stats[$status] = $count;
        }

        return $stats;
    }

    /**
     * Check if alert threshold is exceeded
     * 
     * @return bool
     */
    public function shouldAlert(): bool
    {
        $threshold = $this->params->get('gvend.monitor.alert_threshold');
        $unprocessedCount = $this->gVendRepository->countUnprocessed();
        
        return $unprocessedCount > $threshold;
    }

    /**
     * Clean up old error records
     * 
     * @param int $daysOld
     * @return int Number of records deleted
     */
    public function cleanupOldErrors(int $daysOld = 30): int
    {
        try {
            return $this->connection->executeStatement(
                'DELETE FROM gvend_processing_errors WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
                [$daysOld]
            );
        } catch (\Exception $e) {
            $this->logger->error('Failed to cleanup old error records', ['exception' => $e]);
            return 0;
        }
    }
}
