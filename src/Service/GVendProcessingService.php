<?php

namespace App\Service;

use App\Entity\GVend;
use App\Repository\GVendRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for processing GVend records from third-party vending machine software
 * Handles fiscalization, transaction creation, and payment processing
 */
class GVendProcessingService
{
    public function __construct(
        private GVendRepository $gVendRepository,
        private EntityManagerInterface $entityManager,
        private DeviceService $deviceService,
        private DeviceConfigurationService $deviceConfigurationService,
        private CompanyService $companyService,
        private DeviceTagService $deviceTagService,
        private LocationService $locationService,
        private PaymentService $paymentService,
        private TransactionService $transactionService,
        private FiscalService $fiscalService,
        private LoggerInterface $logger
    ) {}

    /**
     * Process unprocessed GVend records
     * 
     * @param int $batchSize Maximum number of records to process in one batch
     * @return array Processing results with counts and any errors
     */
    public function processUnprocessedRecords(int $batchSize = 50): array
    {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'errors' => []
        ];

        try {
            $unprocessedRecords = $this->gVendRepository->findUnprocessed($batchSize);
            
            if (empty($unprocessedRecords)) {
                $this->logger->info('No unprocessed GVend records found');
                return $results;
            }

            $this->logger->info(sprintf('Processing %d unprocessed GVend records', count($unprocessedRecords)));

            foreach ($unprocessedRecords as $gVend) {
                try {
                    $this->processSingleRecord($gVend);
                    $results['processed']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'id' => $gVend->getId(),
                        'imei' => $gVend->getImei(),
                        'error' => $e->getMessage()
                    ];
                    
                    $this->logger->error(sprintf(
                        'Failed to process GVend record ID %d: %s',
                        $gVend->getId(),
                        $e->getMessage()
                    ), [
                        'gvend_id' => $gVend->getId(),
                        'imei' => $gVend->getImei(),
                        'exception' => $e
                    ]);
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('Error during batch processing: ' . $e->getMessage(), ['exception' => $e]);
            $results['errors'][] = ['batch_error' => $e->getMessage()];
        }

        return $results;
    }

    /**
     * Process a single GVend record
     * 
     * @param GVend $gVend
     * @return void
     * @throws \Exception
     */
    public function processSingleRecord(GVend $gVend): void
    {
        $this->logger->info(sprintf('Processing GVend record ID %d, IMEI: %s', $gVend->getId(), $gVend->getImei()));

        // 1. Get device information
        $device = $this->deviceService->getByLoggerImei($gVend->getImei());
        if (!$device) {
            throw new \Exception(sprintf('Device not found for IMEI: %s', $gVend->getImei()));
        }

        // 2. Get device configuration
        $deviceConfiguration = $this->deviceConfigurationService->getByImei($gVend->getImei());
        if (!$deviceConfiguration) {
            throw new \Exception(sprintf('Device configuration not found for IMEI: %s', $gVend->getImei()));
        }

        // 3. Get company information
        $company = $this->companyService->getById($device['company_id']);
        if (!$company) {
            throw new \Exception(sprintf('Company not found for company_id: %s', $device['company_id']));
        }

        // 4. Get additional required data
        $deviceTags = $this->deviceTagService->getTagsByDeviceId($device['id']);
        $location = $this->locationService->getById($device['location_id']);
        if (!$location) {
            throw new \Exception(sprintf('Location not found for location_id: %s', $device['location_id']));
        }

        // 5. Determine payment type based on GVend type and payment_type
        $paymentType = $this->determinePaymentType($gVend);

        // 6. Begin transaction for atomic processing
        $this->entityManager->beginTransaction();
        
        try {
            // 7. Create transaction record
            $transactionId = $this->transactionService->setTransaction(
                $gVend->getAmount(),
                $paymentType,
                $gVend->getImei(),
                $company['oib'],
                $device,
                $location,
                $deviceTags,
                $gVend->getMoneyRef() ?? 'null'
            );

            // 8. Handle fiscalization if required
            if ($this->shouldFiscalize($device, $location, $paymentType, $company)) {
                $this->fiscalService->fiscal(
                    $company,
                    $gVend->getImei(),
                    $device['bill_number'],
                    $device['business_space_label'],
                    $device['isu_number'],
                    $gVend->getAmount(),
                    $this->mapPaymentTypeForFiscal($paymentType),
                    $transactionId
                );
            }

            // 9. Mark record as processed
            $this->gVendRepository->markAsProcessed($gVend, 1);

            // 10. Commit transaction
            $this->entityManager->commit();

            $this->logger->info(sprintf(
                'Successfully processed GVend record ID %d, created transaction %s',
                $gVend->getId(),
                $transactionId
            ));

        } catch (\Exception $e) {
            $this->entityManager->rollback();
            throw $e;
        }
    }

    /**
     * Determine payment type based on GVend data
     *
     * @param GVend $gVend
     * @return string
     */
    private function determinePaymentType(GVend $gVend): string
    {
        // Map GVend payment_type to system payment types
        // Based on the comments in GVend entity:
        // type: 1-kupovanje kave, 2-uplata na stick, 3-token ili reprezentacija, 7-servisni vending

        switch ($gVend->getType()) {
            case 1: // kupovanje kave (coffee purchase)
                return $this->mapPaymentTypeFromGVend($gVend->getPaymentType());
            case 2: // uplata na stick (stick top-up)
                return 'pay_card'; // Assuming stick payments are card-based
            case 3: // token ili reprezentacija (token or representation)
                return 'pay_token';
            case 7: // servisni vending (service vending)
                return 'pay_coin'; // Default for service
            default:
                return $this->mapPaymentTypeFromGVend($gVend->getPaymentType());
        }
    }

    /**
     * Map GVend payment_type field to system payment types
     *
     * @param string $gVendPaymentType
     * @return string
     */
    private function mapPaymentTypeFromGVend(string $gVendPaymentType): string
    {
        // This mapping may need to be adjusted based on the actual values
        // used by the third-party software
        return match ($gVendPaymentType) {
            '0', 'coin', 'cash' => 'pay_coin',
            '1', 'card', 'credit_card' => 'pay_card',
            '2', 'token' => 'pay_token',
            '3', 'mdb' => 'pay_mdb',
            default => 'pay_coin' // Default fallback
        };
    }

    /**
     * Map system payment type to fiscal service payment type
     *
     * @param string $paymentType
     * @return string
     */
    private function mapPaymentTypeForFiscal(string $paymentType): string
    {
        return match ($paymentType) {
            'pay_card' => 'card',
            'pay_token' => 'token',
            'pay_mdb' => 'coin', // MDB treated as coin for fiscal
            'pay_coin' => 'coin',
            default => 'coin'
        };
    }

    /**
     * Determine if fiscalization is required
     *
     * @param array $device
     * @param array $location
     * @param string $paymentType
     * @param array $company
     * @return bool
     */
    private function shouldFiscalize(array $device, array $location, string $paymentType, array $company): bool
    {
        // Skip fiscalization if device is in service mode
        if ($device['service_mode'] ?? false) {
            return false;
        }

        // Use the existing fiscal check logic from PaymentService
        return $this->paymentService->fiscalCheck($device, $location, $paymentType, $company['is_top_gun'] ?? false);
    }

    /**
     * Get count of unprocessed records
     *
     * @return int
     */
    public function getUnprocessedCount(): int
    {
        return $this->gVendRepository->countUnprocessed();
    }

    /**
     * Mark records as failed for manual review
     *
     * @param array $gVendIds
     * @return int Number of records marked as failed
     */
    public function markAsFailed(array $gVendIds): int
    {
        return $this->gVendRepository->batchUpdateProcessingStatus($gVendIds, -1); // -1 = failed
    }

    /**
     * Reset failed records for reprocessing
     *
     * @param array $gVendIds
     * @return int Number of records reset
     */
    public function resetForReprocessing(array $gVendIds): int
    {
        return $this->gVendRepository->batchUpdateProcessingStatus($gVendIds, 0); // 0 = unprocessed
    }
}
