<?php

namespace App\Command;

use App\Service\GVendProcessingService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:gvend-monitor',
    description: 'Monitor and process new GVend records from third-party vending software'
)]
class GVendMonitorCommand extends Command
{
    private bool $shouldStop = false;
    
    public function __construct(
        private GVendProcessingService $gVendProcessingService,
        private LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Monitor and process new GVend records from third-party vending software')
            ->setHelp('This command runs continuously, checking for new GVend records every 20 seconds and processing them.')
            ->addOption(
                'interval',
                'i',
                InputOption::VALUE_OPTIONAL,
                'Check interval in seconds',
                20
            )
            ->addOption(
                'batch-size',
                'b',
                InputOption::VALUE_OPTIONAL,
                'Number of records to process per batch',
                50
            )
            ->addOption(
                'max-iterations',
                'm',
                InputOption::VALUE_OPTIONAL,
                'Maximum number of iterations (0 = infinite)',
                0
            )
            ->addOption(
                'once',
                'o',
                InputOption::VALUE_NONE,
                'Run once and exit (useful for testing)'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $interval = (int) $input->getOption('interval');
        $batchSize = (int) $input->getOption('batch-size');
        $maxIterations = (int) $input->getOption('max-iterations');
        $runOnce = $input->getOption('once');

        // Validate options
        if ($interval < 1) {
            $io->error('Interval must be at least 1 second');
            return Command::FAILURE;
        }

        if ($batchSize < 1) {
            $io->error('Batch size must be at least 1');
            return Command::FAILURE;
        }

        // Setup signal handlers for graceful shutdown
        $this->setupSignalHandlers($io);

        $io->title('GVend Monitor Started');
        $io->info(sprintf(
            'Configuration: interval=%ds, batch-size=%d, max-iterations=%s',
            $interval,
            $batchSize,
            $maxIterations > 0 ? $maxIterations : 'infinite'
        ));

        $iteration = 0;
        $totalProcessed = 0;
        $totalFailed = 0;

        while (!$this->shouldStop) {
            $iteration++;
            
            try {
                $startTime = microtime(true);
                
                // Check for unprocessed records
                $unprocessedCount = $this->gVendProcessingService->getUnprocessedCount();
                
                if ($unprocessedCount > 0) {
                    $io->info(sprintf(
                        '[Iteration %d] Found %d unprocessed records, processing batch of %d...',
                        $iteration,
                        $unprocessedCount,
                        min($batchSize, $unprocessedCount)
                    ));

                    // Process records
                    $results = $this->gVendProcessingService->processUnprocessedRecords($batchSize);
                    
                    $totalProcessed += $results['processed'];
                    $totalFailed += $results['failed'];

                    // Report results
                    if ($results['processed'] > 0 || $results['failed'] > 0) {
                        $io->success(sprintf(
                            'Processed: %d, Failed: %d (Total: %d processed, %d failed)',
                            $results['processed'],
                            $results['failed'],
                            $totalProcessed,
                            $totalFailed
                        ));
                    }

                    // Log errors if any
                    if (!empty($results['errors'])) {
                        foreach ($results['errors'] as $error) {
                            $io->warning(sprintf(
                                'Error processing record %s: %s',
                                $error['id'] ?? 'unknown',
                                $error['error']
                            ));
                        }
                    }

                } else {
                    $io->text(sprintf('[Iteration %d] No unprocessed records found', $iteration));
                }

                $processingTime = round((microtime(true) - $startTime) * 1000, 2);
                $this->logger->info(sprintf(
                    'GVend monitor iteration %d completed in %sms',
                    $iteration,
                    $processingTime
                ));

            } catch (\Exception $e) {
                $io->error(sprintf('Error during processing: %s', $e->getMessage()));
                $this->logger->error('GVend monitor error', [
                    'iteration' => $iteration,
                    'exception' => $e
                ]);
            }

            // Check if we should stop
            if ($runOnce || ($maxIterations > 0 && $iteration >= $maxIterations)) {
                break;
            }

            // Sleep for the specified interval
            if (!$this->shouldStop) {
                $io->text(sprintf('Waiting %d seconds...', $interval));
                sleep($interval);
            }
        }

        $io->success(sprintf(
            'GVend Monitor stopped after %d iterations. Total: %d processed, %d failed',
            $iteration,
            $totalProcessed,
            $totalFailed
        ));

        return Command::SUCCESS;
    }

    /**
     * Setup signal handlers for graceful shutdown
     */
    private function setupSignalHandlers(SymfonyStyle $io): void
    {
        if (!function_exists('pcntl_signal')) {
            $io->note('PCNTL extension not available. Graceful shutdown via signals not supported.');
            return;
        }

        // Handle SIGTERM and SIGINT for graceful shutdown
        pcntl_signal(SIGTERM, function () use ($io) {
            $io->warning('Received SIGTERM, stopping gracefully...');
            $this->shouldStop = true;
        });

        pcntl_signal(SIGINT, function () use ($io) {
            $io->warning('Received SIGINT (Ctrl+C), stopping gracefully...');
            $this->shouldStop = true;
        });

        // Enable signal handling
        pcntl_async_signals(true);
    }
}
